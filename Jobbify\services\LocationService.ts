import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Location from 'expo-location';
import { LocationSettings } from '@/components/LocationSettingsModal';

const LOCATION_SETTINGS_KEY = 'locationSettings';
const LAST_LOCATION_UPDATE_KEY = 'lastLocationUpdate';

// Default location settings
export const defaultLocationSettings: LocationSettings = {
  userLocation: '',
  searchRadius: 25,
  includeRemote: true,
  autoUpdateLocation: false
};

/**
 * Get stored location settings
 */
export const getLocationSettings = async (): Promise<LocationSettings> => {
  try {
    const stored = await AsyncStorage.getItem(LOCATION_SETTINGS_KEY);
    if (stored) {
      return { ...defaultLocationSettings, ...JSON.parse(stored) };
    }
  } catch (error) {
    console.error('Error getting location settings:', error);
  }
  return defaultLocationSettings;
};

/**
 * Save location settings
 */
export const saveLocationSettings = async (settings: LocationSettings): Promise<void> => {
  try {
    await AsyncStorage.setItem(LOCATION_SETTINGS_KEY, JSON.stringify(settings));
  } catch (error) {
    console.error('Error saving location settings:', error);
    throw error;
  }
};

/**
 * Get current device location
 */
export const getCurrentLocation = async (): Promise<{ latitude: number; longitude: number } | null> => {
  try {
    // Request permission
    const { status } = await Location.requestForegroundPermissionsAsync();
    if (status !== 'granted') {
      console.log('Location permission denied');
      return null;
    }

    // Get current position
    const location = await Location.getCurrentPositionAsync({
      accuracy: Location.Accuracy.Balanced,
      timeInterval: 10000,
      distanceInterval: 100
    });

    return {
      latitude: location.coords.latitude,
      longitude: location.coords.longitude
    };
  } catch (error) {
    console.error('Error getting current location:', error);
    return null;
  }
};

/**
 * Check if location should be auto-updated
 */
export const shouldUpdateLocation = async (): Promise<boolean> => {
  try {
    const settings = await getLocationSettings();
    if (!settings.autoUpdateLocation) {
      return false;
    }

    const lastUpdate = await AsyncStorage.getItem(LAST_LOCATION_UPDATE_KEY);
    if (!lastUpdate) {
      return true;
    }

    const lastUpdateTime = parseInt(lastUpdate, 10);
    const now = Date.now();
    const hoursSinceUpdate = (now - lastUpdateTime) / (1000 * 60 * 60);

    // Update if it's been more than 6 hours
    return hoursSinceUpdate >= 6;
  } catch (error) {
    console.error('Error checking if location should update:', error);
    return false;
  }
};

/**
 * Auto-update user location if enabled and needed
 */
export const autoUpdateLocationIfNeeded = async (): Promise<LocationSettings | null> => {
  try {
    const shouldUpdate = await shouldUpdateLocation();
    if (!shouldUpdate) {
      return null;
    }

    const currentLocation = await getCurrentLocation();
    if (!currentLocation) {
      return null;
    }

    const settings = await getLocationSettings();
    const locationString = `Current Location (${currentLocation.latitude.toFixed(2)}, ${currentLocation.longitude.toFixed(2)})`;
    
    const updatedSettings: LocationSettings = {
      ...settings,
      userLocation: locationString,
      coordinates: currentLocation
    };

    await saveLocationSettings(updatedSettings);
    await AsyncStorage.setItem(LAST_LOCATION_UPDATE_KEY, Date.now().toString());

    return updatedSettings;
  } catch (error) {
    console.error('Error auto-updating location:', error);
    return null;
  }
};

/**
 * Reverse geocode coordinates to get address
 * This is a placeholder - in a real app you'd use a geocoding service
 */
export const reverseGeocode = async (
  latitude: number,
  longitude: number
): Promise<string | null> => {
  try {
    const result = await Location.reverseGeocodeAsync({ latitude, longitude });
    if (result && result.length > 0) {
      const address = result[0];
      const parts = [];
      
      if (address.city) parts.push(address.city);
      if (address.region) parts.push(address.region);
      
      return parts.join(', ') || `${latitude.toFixed(2)}, ${longitude.toFixed(2)}`;
    }
  } catch (error) {
    console.error('Error reverse geocoding:', error);
  }
  
  return `${latitude.toFixed(2)}, ${longitude.toFixed(2)}`;
};

/**
 * Get formatted location string for display
 */
export const getFormattedLocation = (settings: LocationSettings): string => {
  if (!settings.userLocation) {
    return 'No location set';
  }
  
  if (settings.userLocation.includes('Current Location')) {
    return settings.userLocation;
  }
  
  return settings.userLocation;
};

/**
 * Get location summary for display
 */
export const getLocationSummary = (settings: LocationSettings): string => {
  const location = getFormattedLocation(settings);
  const radius = settings.searchRadius;
  const remote = settings.includeRemote ? ' + Remote' : '';
  
  if (location === 'No location set') {
    return settings.includeRemote ? 'Remote jobs only' : 'No location filter';
  }
  
  return `${location} (${radius} mi)${remote}`;
};

/**
 * Check if location permissions are granted
 */
export const hasLocationPermission = async (): Promise<boolean> => {
  try {
    const { status } = await Location.getForegroundPermissionsAsync();
    return status === 'granted';
  } catch (error) {
    console.error('Error checking location permission:', error);
    return false;
  }
};

/**
 * Request location permissions
 */
export const requestLocationPermission = async (): Promise<boolean> => {
  try {
    const { status } = await Location.requestForegroundPermissionsAsync();
    return status === 'granted';
  } catch (error) {
    console.error('Error requesting location permission:', error);
    return false;
  }
};

/**
 * Clear all location data
 */
export const clearLocationData = async (): Promise<void> => {
  try {
    await AsyncStorage.multiRemove([LOCATION_SETTINGS_KEY, LAST_LOCATION_UPDATE_KEY]);
  } catch (error) {
    console.error('Error clearing location data:', error);
    throw error;
  }
};

/**
 * Validate location settings
 */
export const validateLocationSettings = (settings: Partial<LocationSettings>): LocationSettings => {
  return {
    userLocation: settings.userLocation || '',
    coordinates: settings.coordinates,
    searchRadius: Math.max(5, Math.min(100, settings.searchRadius || 25)),
    includeRemote: settings.includeRemote !== false,
    autoUpdateLocation: settings.autoUpdateLocation === true
  };
};

export type { LocationSettings };