{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,gBAAgB,GAGjB,MAAM,mBAAmB,CAAC;AAE3B,OAAO,EAAE,oBAAoB,IAAI,YAAY,EAAE,MAAM,wBAAwB,CAAC;AAC9E,OAAO,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAC3D,OAAO,EAAE,6BAA6B,EAAE,MAAM,uBAAuB,CAAC;AAEtE,cAAc,YAAY,CAAC;AAC3B,cAAc,kBAAkB,CAAC;AAEjC,OAAO,EACL,gBAAgB,IAAI,QAAQ,EAC5B,oBAAoB,IAAI,YAAY,EACpC,2BAA2B,IAAI,mBAAmB,EAClD,6BAA6B,IAAI,qBAAqB,GACvD,MAAM,kBAAkB,CAAC", "sourcesContent": ["export {\n  PermissionStatus,\n  type PermissionHookOptions,\n  type PermissionExpiration,\n} from 'expo-modules-core';\n\nexport { LocationEventEmitter as EventEmitter } from './LocationEventEmitter';\nexport { _getCurrentWatchId } from './LocationSubscribers';\nexport { installWebGeolocationPolyfill } from './GeolocationPolyfill';\n\nexport * from './Location';\nexport * from './Location.types';\n\nexport {\n  LocationAccuracy as Accuracy,\n  LocationActivityType as ActivityType,\n  LocationGeofencingEventType as GeofencingEventType,\n  LocationGeofencingRegionState as GeofencingRegionState,\n} from './Location.types';\n"]}