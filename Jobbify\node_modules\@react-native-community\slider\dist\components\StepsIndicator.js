Object.defineProperty(exports,"__esModule",{value:true});exports.StepsIndicator=void 0;var _react=_interopRequireWildcard(require("react"));var _reactNative=require("react-native");var _StepNumber=require("./StepNumber");var _TrackMark=require("./TrackMark");var _styles=require("../utils/styles");var _constants=require("../utils/constants");var _jsxRuntime=require("react/jsx-runtime");var _this=this,_jsxFileName="/Users/<USER>/Desktop/Projects/react-native-slider/package/src/components/StepsIndicator.tsx";function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap(),t=new WeakMap();return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?t:r;})(e);}function _interopRequireWildcard(e,r){if(!r&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=_getRequireWildcardCache(r);if(t&&t.has(e))return t.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var u in e)if("default"!==u&&{}.hasOwnProperty.call(e,u)){var i=a?Object.getOwnPropertyDescriptor(e,u):null;i&&(i.get||i.set)?Object.defineProperty(n,u,i):n[u]=e[u];}return n.default=e,t&&t.set(e,n),n;}var StepsIndicator=exports.StepsIndicator=function StepsIndicator(_ref){var options=_ref.options,sliderWidth=_ref.sliderWidth,currentValue=_ref.currentValue,StepMarker=_ref.StepMarker,renderStepNumber=_ref.renderStepNumber,thumbImage=_ref.thumbImage,isLTR=_ref.isLTR;var stepNumberFontStyle=(0,_react.useMemo)(function(){return{fontSize:options.length>9?_constants.constants.STEP_NUMBER_TEXT_FONT_SMALL:_constants.constants.STEP_NUMBER_TEXT_FONT_BIG};},[options.length]);var values=isLTR?options.reverse():options;var renderStepIndicator=(0,_react.useCallback)(function(i,index){return(0,_jsxRuntime.jsx)(_react.Fragment,{children:(0,_jsxRuntime.jsxs)(_reactNative.View,{style:_styles.styles.stepIndicatorElement,children:[(0,_jsxRuntime.jsx)(_TrackMark.SliderTrackMark,{isTrue:currentValue===i,index:i,thumbImage:thumbImage,StepMarker:StepMarker,currentValue:currentValue,min:options[0],max:options[options.length-1]},`${index}-SliderTrackMark`),renderStepNumber?(0,_jsxRuntime.jsx)(_StepNumber.StepNumber,{i:i,style:stepNumberFontStyle},`${index}th-step`):null]},`${index}-View`)},index);},[currentValue,StepMarker,options,thumbImage,renderStepNumber,stepNumberFontStyle]);return(0,_jsxRuntime.jsx)(_reactNative.View,{pointerEvents:"none",style:[_styles.styles.stepsIndicator,{marginHorizontal:sliderWidth*_constants.constants.MARGIN_HORIZONTAL_PADDING}],children:values.map(function(i,index){return renderStepIndicator(i,index);})});};