import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Modal,
  SafeAreaView,
  FlatList,
  Alert,
  ActivityIndicator
} from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import { useAppContext } from '@/context/AppContext';
import { LightTheme, DarkTheme } from '@/constants/Theme';
import { getUserLocation } from '@/services/JobsService';

interface LocationPickerModalProps {
  visible: boolean;
  onClose: () => void;
  onLocationSelect: (location: string) => void;
  currentLocation?: string;
}

const popularLocations = [
  'San Francisco, CA',
  'New York, NY',
  'Los Angeles, CA',
  'Seattle, WA',
  'Austin, TX',
  'Boston, MA',
  'Chicago, IL',
  'Denver, CO',
  'Atlanta, GA',
  'Miami, FL',
  'Dallas, TX',
  'Portland, OR',
  'Remote',
  'Anywhere'
];

export default function LocationPickerModal({
  visible,
  onClose,
  onLocationSelect,
  currentLocation
}: LocationPickerModalProps) {
  const { theme } = useAppContext();
  const themeColors = theme === 'light' ? LightTheme : DarkTheme;
  
  const [searchQuery, setSearchQuery] = useState('');
  const [isGettingLocation, setIsGettingLocation] = useState(false);
  const [userCurrentLocation, setUserCurrentLocation] = useState<string | null>(null);

  const filteredLocations = popularLocations.filter(location =>
    location.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleLocationSelect = (location: string) => {
    onLocationSelect(location);
    onClose();
  };

  const handleUseCurrentLocation = async () => {
    setIsGettingLocation(true);
    try {
      const location = await getUserLocation();
      if (location) {
        // In a real app, you'd use reverse geocoding to get the city name
        // For now, we'll use a placeholder
        const locationString = `Current Location (${location.latitude.toFixed(2)}, ${location.longitude.toFixed(2)})`;
        setUserCurrentLocation(locationString);
        handleLocationSelect(locationString);
      } else {
        Alert.alert(
          'Location Access Denied',
          'Please enable location permissions in your device settings to use this feature.'
        );
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to get your current location. Please try again.');
    } finally {
      setIsGettingLocation(false);
    }
  };

  const renderLocationItem = ({ item }: { item: string }) => (
    <TouchableOpacity
      style={[
        styles.locationItem,
        { 
          backgroundColor: themeColors.card,
          borderBottomColor: themeColors.border
        }
      ]}
      onPress={() => handleLocationSelect(item)}
    >
      <FontAwesome 
        name={item === 'Remote' || item === 'Anywhere' ? 'globe' : 'map-marker'} 
        size={16} 
        color={themeColors.textSecondary} 
      />
      <Text style={[styles.locationText, { color: themeColors.text }]}>
        {item}
      </Text>
      {currentLocation === item && (
        <FontAwesome name="check" size={16} color={themeColors.tint} />
      )}
    </TouchableOpacity>
  );

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <SafeAreaView style={[styles.container, { backgroundColor: themeColors.background }]}>
        {/* Header */}
        <View style={[styles.header, { borderBottomColor: themeColors.border }]}>
          <TouchableOpacity onPress={onClose}>
            <FontAwesome name="times" size={20} color={themeColors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: themeColors.text }]}>
            Select Location
          </Text>
          <View style={{ width: 20 }} />
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <View style={[styles.searchBar, { 
            backgroundColor: themeColors.card,
            borderColor: themeColors.border
          }]}>
            <FontAwesome name="search" size={16} color={themeColors.textSecondary} />
            <TextInput
              style={[styles.searchInput, { color: themeColors.text }]}
              placeholder="Search locations..."
              placeholderTextColor={themeColors.textSecondary}
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={() => setSearchQuery('')}>
                <FontAwesome name="times-circle" size={16} color={themeColors.textSecondary} />
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Current Location Button */}
        <View style={styles.currentLocationContainer}>
          <TouchableOpacity
            style={[
              styles.currentLocationButton,
              { 
                backgroundColor: themeColors.tint,
                opacity: isGettingLocation ? 0.6 : 1
              }
            ]}
            onPress={handleUseCurrentLocation}
            disabled={isGettingLocation}
          >
            {isGettingLocation ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <FontAwesome name="location-arrow" size={16} color="#FFFFFF" />
            )}
            <Text style={styles.currentLocationText}>
              {isGettingLocation ? 'Getting location...' : 'Use Current Location'}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Clear Location Option */}
        {currentLocation && (
          <View style={styles.clearLocationContainer}>
            <TouchableOpacity
              style={[styles.clearLocationButton, { borderColor: themeColors.border }]}
              onPress={() => handleLocationSelect('')}
            >
              <FontAwesome name="times" size={16} color={themeColors.textSecondary} />
              <Text style={[styles.clearLocationText, { color: themeColors.textSecondary }]}>
                Clear Location Filter
              </Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Popular Locations */}
        <View style={styles.locationsContainer}>
          <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
            {searchQuery ? 'Search Results' : 'Popular Locations'}
          </Text>
          
          <FlatList
            data={filteredLocations}
            renderItem={renderLocationItem}
            keyExtractor={(item) => item}
            showsVerticalScrollIndicator={false}
            style={styles.locationsList}
          />
        </View>
      </SafeAreaView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderRadius: 8,
    borderWidth: 1,
    gap: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
  },
  currentLocationContainer: {
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  currentLocationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  currentLocationText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
  clearLocationContainer: {
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  clearLocationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    gap: 8,
  },
  clearLocationText: {
    fontSize: 16,
    fontWeight: '500',
  },
  locationsContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  locationsList: {
    flex: 1,
  },
  locationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    gap: 12,
  },
  locationText: {
    flex: 1,
    fontSize: 16,
  },
});
