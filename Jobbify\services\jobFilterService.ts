/**
 * Job Filter Service
 * Handles job filtering logic and user filter preferences
 */

import { Job } from '@/context/AppContext';
import { UserJobPreferences } from './jobRecommendationService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { LocationSettings } from '@/services/LocationService';
import { filterJobsByLocation } from '@/services/JobsService';

export interface JobFilters {
  location: string;
  jobTypes: string[];
  experienceLevel: string;
  salaryRange: [number, number];
  minSalary: number;
  maxSalary: number;
  skills: string[];
  company: string;
  companySize?: string[];
  industries: string[];
  datePosted: string;
  maxDistance: number;
  remoteOnly: boolean;
  hasLogo: boolean;
  postedWithin?: number;
  quickFilters: {
    remote: boolean;
    fullTime: boolean;
    highPay: boolean;
    recentlyPosted: boolean;
  };
  // Enhanced location settings
  locationSettings?: LocationSettings;
}

export const defaultFilters: JobFilters = {
  location: '',
  jobTypes: [],
  experienceLevel: '',
  salaryRange: [0, 200000],
  minSalary: 0,
  maxSalary: 500000,
  skills: [],
  company: '',
  companySize: [],
  industries: [],
  datePosted: '',
  maxDistance: 25,
  remoteOnly: false,
  hasLogo: false,
  postedWithin: undefined,
  quickFilters: {
    remote: false,
    fullTime: false,
    highPay: false,
    recentlyPosted: false,
  },
};

/**
 * Apply filters to a list of jobs
 */
export const applyFilters = (jobs: Job[], filters: JobFilters): Job[] => {
  let filteredJobs = jobs;

  // Enhanced location filtering
  if (filters.locationSettings) {
    filteredJobs = filterJobsByLocation(
      filteredJobs,
      filters.locationSettings.userLocation,
      filters.locationSettings.coordinates,
      filters.locationSettings.searchRadius,
      filters.locationSettings.includeRemote
    );
  } else if (filters.location || filters.remoteOnly) {
    // Fallback to legacy location filtering
    filteredJobs = filteredJobs.filter(job => {
      if (filters.remoteOnly) {
        const jobLocation = job.location.toLowerCase();
        return jobLocation.includes('remote') || jobLocation.includes('anywhere');
      }
      
      if (filters.location) {
        const jobLocation = job.location.toLowerCase();
        const filterLocation = filters.location.toLowerCase();
        return jobLocation.includes(filterLocation) || jobLocation.includes('remote');
      }
      
      return true;
    });
  }

  // Apply other filters
  return filteredJobs.filter(job => {
    // Job type filter
    if (filters.jobTypes.length > 0) {
      const jobType = job.type?.toLowerCase() || '';
      const matchesType = filters.jobTypes.some(type =>
        jobType.includes(type.toLowerCase())
      );
      if (!matchesType) {
        return false;
      }
    }

    // Experience level filter
    if (filters.experienceLevel) {
      const jobLevel = job.experienceLevel?.toLowerCase() || '';
      if (!jobLevel.includes(filters.experienceLevel.toLowerCase())) {
        return false;
      }
    }

    // Salary range filter
    if (job.salary) {
      const minSalary = job.salary.min || 0;
      const maxSalary = job.salary.max || 0;
      
      // Check if job salary range overlaps with filter range
      if (maxSalary > 0 && (maxSalary < filters.minSalary || minSalary > filters.maxSalary)) {
        return false;
      }
    } else if (job.pay) {
      // Fallback to pay field if salary object is not available
      const salary = parseInt(job.pay.replace(/[^\d]/g, '')) || 0;
      if (salary > 0 && (salary < filters.minSalary || salary > filters.maxSalary)) {
        return false;
      }
    }

    // Skills filter
    if (filters.skills.length > 0) {
      const jobSkills = job.skills?.map((skill: string) => skill.toLowerCase()) || [];
      const hasMatchingSkill = filters.skills.some((skill: string) =>
        jobSkills.some((jobSkill: string) => jobSkill.includes(skill.toLowerCase()))
      );
      if (!hasMatchingSkill) {
        return false;
      }
    }

    // Company filter
    if (filters.company) {
      const jobCompany = job.company.toLowerCase();
      if (!jobCompany.includes(filters.company.toLowerCase())) {
        return false;
      }
    }

    // Date posted filter
    if (filters.datePosted && job.postedDate) {
      const postedDate = new Date(job.postedDate);
      const now = new Date();
      const daysDiff = Math.floor((now.getTime() - postedDate.getTime()) / (1000 * 60 * 60 * 24));
      
      switch (filters.datePosted) {
        case 'today':
          if (daysDiff > 0) return false;
          break;
        case 'week':
          if (daysDiff > 7) return false;
          break;
        case 'month':
          if (daysDiff > 30) return false;
          break;
        default:
          break;
      }
    }

    return true;
  });
};

/**
 * Convert user preferences to filters
 */
export const preferencesToFilters = (preferences: UserJobPreferences): JobFilters => {
  const minSalary = preferences.min_salary || 0;
  const maxSalary = preferences.max_salary || 500000;

  return {
    location: preferences.preferred_locations[0] || '',
    maxDistance: preferences.max_commute_distance,
    remoteOnly: preferences.remote_work_preference === 'required',
    jobTypes: preferences.preferred_job_types,
    experienceLevel: preferences.experience_level,
    salaryRange: [minSalary, maxSalary],
    minSalary,
    maxSalary,
    skills: [],
    company: '',
    companySize: preferences.preferred_company_sizes,
    industries: preferences.preferred_industries,
    datePosted: '',
    postedWithin: 30,
    hasLogo: false,
    quickFilters: {
      remote: preferences.remote_work_preference === 'required',
      fullTime: preferences.preferred_job_types.includes('Full-time'),
      highPay: minSalary >= 100000,
      recentlyPosted: false,
    },
    locationSettings: undefined
  };
};

/**
 * Save user's current filter settings
 */
export const saveFilterSettings = async (userId: string, filters: JobFilters): Promise<void> => {
  try {
    await AsyncStorage.setItem(`job_filters_${userId}`, JSON.stringify(filters));
  } catch (error) {
    console.error('Error saving filter settings:', error);
  }
};

/**
 * Load user's saved filter settings
 */
export const loadFilterSettings = async (userId: string): Promise<JobFilters> => {
  try {
    const saved = await AsyncStorage.getItem(`job_filters_${userId}`);
    if (saved) {
      return { ...defaultFilters, ...JSON.parse(saved) };
    }
  } catch (error) {
    console.error('Error loading filter settings:', error);
  }
  return defaultFilters;
};

/**
 * Get filter summary for display
 */
export const getFilterSummary = (filters: JobFilters): string => {
  const activeFilters: string[] = [];
  
  if (filters.location) {
    activeFilters.push(`Location: ${filters.location}`);
  }
  
  if (filters.remoteOnly) {
    activeFilters.push('Remote only');
  }
  
  if (filters.jobTypes.length > 0) {
    activeFilters.push(`Types: ${filters.jobTypes.join(', ')}`);
  }
  
  if (filters.minSalary > 0 || filters.maxSalary < 500000) {
    const min = filters.minSalary > 0 ? `$${filters.minSalary.toLocaleString()}` : 'Any';
    const max = filters.maxSalary < 500000 ? `$${filters.maxSalary.toLocaleString()}` : 'Any';
    activeFilters.push(`Salary: ${min} - ${max}`);
  }
  
  if (filters.industries.length > 0) {
    activeFilters.push(`Industries: ${filters.industries.join(', ')}`);
  }
  
  const quickFilters = Object.entries(filters.quickFilters)
    .filter(([_, active]) => active)
    .map(([key, _]) => key.charAt(0).toUpperCase() + key.slice(1));
  
  if (quickFilters.length > 0) {
    activeFilters.push(...quickFilters);
  }
  
  return activeFilters.length > 0 ? activeFilters.join(' • ') : 'No filters applied';
};

/**
 * Count active filters
 */
export const countActiveFilters = (filters: JobFilters): number => {
  let count = 0;
  
  if (filters.location) count++;
  if (filters.remoteOnly) count++;
  if (filters.jobTypes.length > 0) count++;
  if (filters.minSalary > 0 || filters.maxSalary < 500000) count++;
  if (filters.industries.length > 0) count++;
  if (filters.hasLogo) count++;
  
  count += Object.values(filters.quickFilters).filter(Boolean).length;
  
  return count;
};
