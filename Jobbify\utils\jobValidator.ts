/**
 * Job validation utilities for ensuring jobs meet display requirements
 */

export interface JobDisplayRequirements {
  hasDescription: boolean;
  hasImage: boolean;
  meetsMinimumRequirements: boolean;
}

/**
 * Check if a job has a valid description for display
 * Requires approximately 500 words (2500 characters minimum)
 */
export function hasValidDescription(job: any, minLength: number = 2500): boolean {
  const description = job?.description || job?.cleanDescription || '';

  if (!description || typeof description !== 'string') {
    return false;
  }

  const cleanDesc = description.trim();

  // Require substantial description (approximately 500 words)
  if (cleanDesc.length < minLength) {
    return false;
  }

  // Additional check: count actual words to ensure quality
  const wordCount = cleanDesc.split(/\s+/).filter(word => word.length > 0).length;
  if (wordCount < 500) {
    return false;
  }

  return true;
}

/**
 * Check if a job has a valid image/logo for display
 */
export function hasValidImage(job: any): boolean {
  // Check for existing image URLs
  const imageFields = ['logo', 'image', 'companyLogo', 'fallbackImage'];
  
  for (const field of imageFields) {
    const imageUrl = job?.[field];
    if (imageUrl && typeof imageUrl === 'string' && imageUrl.trim()) {
      const url = imageUrl.trim();
      // Check if it's a valid URL
      if (url.startsWith('http://') || url.startsWith('https://') || url.startsWith('data:')) {
        return true;
      }
    }
  }
  
  // Check if we can generate fallback from company name
  const company = job?.company;
  if (company && typeof company === 'string' && company.trim().length > 0) {
    return true; // We can always generate a fallback image
  }
  
  return false;
}

/**
 * Validate if a job meets minimum requirements for panel display
 */
export function validateJobForDisplay(job: any): JobDisplayRequirements {
  const hasDescription = hasValidDescription(job);
  const hasImage = hasValidImage(job);
  const meetsMinimumRequirements = hasDescription && hasImage;
  
  return {
    hasDescription,
    hasImage,
    meetsMinimumRequirements
  };
}

/**
 * Filter jobs to only include those suitable for display
 */
export function filterJobsForDisplay(jobs: any[]): any[] {
  if (!Array.isArray(jobs)) {
    return [];
  }
  
  return jobs.filter(job => {
    const validation = validateJobForDisplay(job);
    return validation.meetsMinimumRequirements;
  });
}

/**
 * Generate fallback image URL for a company
 */
export function generateFallbackImage(companyName: string): string {
  const cleanName = companyName.replace(/[^a-zA-Z0-9\s]/g, '').trim();
  return `https://ui-avatars.com/api/?name=${encodeURIComponent(cleanName)}&background=random&size=150`;
}

/**
 * Generate a detailed 500+ word job description
 */
function generateDetailedJobDescription(job: any): string {
  const company = job.company || 'a leading company';
  const title = job.title || 'this position';
  const location = job.location || 'flexible location';
  const category = job.category || 'technology';
  const jobType = job.jobType || 'full-time';

  return `
**About the Role**

We are seeking a talented ${title} to join our dynamic team at ${company}. This ${jobType} position offers an exciting opportunity to work in ${location} with a company that values innovation, collaboration, and professional growth.

**Position Overview**

As a ${title}, you will play a crucial role in driving our company's success and contributing to our mission of delivering exceptional results. This role is designed for professionals who are passionate about their craft and eager to make a meaningful impact in the ${category} industry.

**Key Responsibilities**

• Lead and execute strategic initiatives that align with company objectives
• Collaborate with cross-functional teams to deliver high-quality solutions
• Analyze complex problems and develop innovative approaches to solve them
• Mentor junior team members and contribute to knowledge sharing
• Participate in code reviews, design discussions, and technical planning sessions
• Stay current with industry trends and best practices in ${category}
• Contribute to process improvements and operational excellence
• Work closely with stakeholders to understand requirements and deliver solutions
• Maintain high standards of quality and attention to detail
• Support continuous learning and development initiatives

**What We're Looking For**

The ideal candidate will have a strong background in ${category} with proven experience in similar roles. We value individuals who demonstrate:

• Strong technical skills and problem-solving abilities
• Excellent communication and interpersonal skills
• Ability to work independently and as part of a team
• Passion for learning and staying updated with new technologies
• Experience with project management and meeting deadlines
• Strong analytical thinking and attention to detail
• Adaptability and flexibility in a fast-paced environment

**What We Offer**

At ${company}, we believe in investing in our people. We offer:

• Competitive salary and comprehensive benefits package
• Flexible working arrangements and work-life balance
• Professional development opportunities and career advancement
• Access to cutting-edge technology and tools
• Collaborative and inclusive work environment
• Health and wellness programs
• Retirement savings plans with company matching
• Paid time off and holiday benefits
• Learning and development budget for skill enhancement
• Team building activities and company events

**Company Culture**

${company} is committed to fostering a diverse and inclusive workplace where all employees can thrive. We believe in the power of different perspectives and experiences to drive innovation and success. Our culture emphasizes collaboration, respect, and continuous improvement.

**Growth Opportunities**

This role offers significant opportunities for professional growth and career advancement. You'll have the chance to work on challenging projects, learn from experienced professionals, and develop new skills that will advance your career in ${category}.

**Next Steps**

If you're ready to take the next step in your career and join a company that values your contributions, we'd love to hear from you. This position offers the perfect blend of challenge, growth, and reward for the right candidate.

Join our team and be part of a company that's shaping the future of ${category}. Apply today and start your journey with ${company}!
  `.trim();
}

/**
 * Ensure job has required fields for display, adding fallbacks if needed
 */
export function enhanceJobForDisplay(job: any): any {
  const enhanced = { ...job };
  
  // Ensure description meets 500-word minimum
  if (!hasValidDescription(enhanced)) {
    enhanced.description = generateDetailedJobDescription(enhanced);
  }
  
  // Ensure image
  if (!hasValidImage(enhanced)) {
    const company = enhanced.company || 'Company';
    enhanced.logo = generateFallbackImage(company);
    enhanced.fallbackImage = enhanced.logo;
  }
  
  return enhanced;
}
