def isNewArchitectureEnabled() {
  return project.hasProperty("newArchEnabled") && project.newArchEnabled == "true"
}

if (isNewArchitectureEnabled()) {
  buildscript {
    repositories {
      mavenCentral()
      google()
    }

    dependencies {
      classpath("com.android.tools.build:gradle:7.1.1")
      classpath("com.facebook.react:react-native-gradle-plugin")
      classpath("de.undercouch:gradle-download-task:5.0.1")
    }
  }
}

apply plugin: 'com.android.library'
if (isNewArchitectureEnabled()) {
  apply plugin: 'com.facebook.react'
}

def getExtOrDefault(name) {
  return rootProject.ext.has(name) ? rootProject.ext.get(name) : project.properties['ReactNativeSlider_' + name]
}

def getExtOrIntegerDefault(name) {
  return rootProject.ext.has(name) ? rootProject.ext.get(name) : (project.properties['ReactNativeSlider_' + name]).toInteger()
}

android {

  def agpVersion = com.android.Version.ANDROID_GRADLE_PLUGIN_VERSION.tokenize('.')
  // Check AGP version for backward compatibility w/react-native versions still on gradle plugin 6
  def major = agpVersion[0].toInteger()
  def minor = agpVersion[1].toInteger()
  if ((major == 7 && minor >= 3) || major >= 8) {
    namespace "com.reactnativecommunity.slider"
    buildFeatures {
      buildConfig true
    }
  }

  compileSdkVersion getExtOrIntegerDefault('compileSdkVersion')
  buildToolsVersion getExtOrDefault('buildToolsVersion')

  defaultConfig {
    minSdkVersion getExtOrIntegerDefault('minSdkVersion')
    targetSdkVersion getExtOrIntegerDefault('targetSdkVersion')
    buildConfigField "boolean", "IS_NEW_ARCHITECTURE_ENABLED", isNewArchitectureEnabled().toString()
  }

  sourceSets {
    main {
      if (isNewArchitectureEnabled()) {
          java.srcDirs += ['src/newarch', "${project.buildDir}/generated/source/codegen/java"]
      } else {
          java.srcDirs += ['src/oldarch']
      }
    }
  }
}

repositories {
  google()
  mavenCentral()
}

dependencies {
  //noinspection GradleDynamicVersion
  api 'com.facebook.react:react-native:+'
}

