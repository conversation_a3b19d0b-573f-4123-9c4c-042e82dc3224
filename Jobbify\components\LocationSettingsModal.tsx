import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Modal,
  SafeAreaView,
  FlatList,
  Alert,
  ActivityIndicator,
  Switch
} from 'react-native';
import Slider from '@react-native-community/slider';
import { FontAwesome, MaterialIcons } from '@expo/vector-icons';
import { useAppContext } from '@/context/AppContext';
import { LightTheme, DarkTheme } from '@/constants/Theme';
import { getUserLocation } from '@/services/JobsService';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface LocationSettings {
  userLocation: string;
  coordinates?: { latitude: number; longitude: number };
  searchRadius: number; // in miles
  includeRemote: boolean;
  autoUpdateLocation: boolean;
}

interface LocationSettingsModalProps {
  visible: boolean;
  onClose: () => void;
  onSave: (settings: LocationSettings) => Promise<void>;
  onSettingsUpdate?: (settings: LocationSettings) => void;
  currentSettings?: LocationSettings;
}

const popularLocations = [
  'San Francisco, CA',
  'New York, NY',
  'Los Angeles, CA',
  'Seattle, WA',
  'Austin, TX',
  'Boston, MA',
  'Chicago, IL',
  'Denver, CO',
  'Atlanta, GA',
  'Miami, FL',
  'Dallas, TX',
  'Portland, OR',
  'Remote',
  'Anywhere'
];

const defaultSettings: LocationSettings = {
  userLocation: '',
  searchRadius: 25,
  includeRemote: true,
  autoUpdateLocation: false
};

export default function LocationSettingsModal({
  visible,
  onClose,
  onSave,
  onSettingsUpdate,
  currentSettings
}: LocationSettingsModalProps) {
  const { theme } = useAppContext();
  const themeColors = theme === 'light' ? LightTheme : DarkTheme;
  
  const [settings, setSettings] = useState<LocationSettings>(currentSettings || defaultSettings);
  const [searchQuery, setSearchQuery] = useState('');
  const [isGettingLocation, setIsGettingLocation] = useState(false);
  const [activeTab, setActiveTab] = useState<'location' | 'radius'>('location');

  const filteredLocations = popularLocations.filter(location =>
    location.toLowerCase().includes(searchQuery.toLowerCase())
  );

  useEffect(() => {
    if (currentSettings) {
      setSettings(currentSettings);
    }
  }, [currentSettings]);

  const handleLocationSelect = (location: string) => {
    setSettings(prev => ({ ...prev, userLocation: location, coordinates: undefined }));
  };

  const handleUseCurrentLocation = async () => {
    setIsGettingLocation(true);
    try {
      const location = await getUserLocation();
      if (location) {
        const locationString = `Current Location (${location.latitude.toFixed(2)}, ${location.longitude.toFixed(2)})`;
        setSettings(prev => ({
          ...prev,
          userLocation: locationString,
          coordinates: location
        }));
      } else {
        Alert.alert(
          'Location Access Denied',
          'Please enable location permissions in your device settings to use this feature.'
        );
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to get your current location. Please try again.');
    }
    setIsGettingLocation(false);
  };

  const handleRadiusChange = (value: number) => {
    setSettings(prev => ({ ...prev, searchRadius: Math.round(value) }));
  };

  const handleSaveSettings = async () => {
    try {
      await AsyncStorage.setItem('locationSettings', JSON.stringify(settings));
      await onSave(settings);
      if (onSettingsUpdate) {
        onSettingsUpdate(settings);
      }
      onClose();
    } catch (error) {
      console.error('Error saving location settings:', error);
      Alert.alert('Error', 'Failed to save location settings. Please try again.');
    }
  };

  const renderLocationItem = ({ item }: { item: string }) => (
    <TouchableOpacity
      style={[
        styles.locationItem,
        {
          backgroundColor: settings.userLocation === item ? themeColors.tint : 'transparent',
          borderColor: themeColors.border
        }
      ]}
      onPress={() => handleLocationSelect(item)}
    >
      <MaterialIcons
        name="location-on"
        size={20}
        color={settings.userLocation === item ? '#FFFFFF' : themeColors.textSecondary}
      />
      <Text style={[
        styles.locationText,
        { color: settings.userLocation === item ? '#FFFFFF' : themeColors.text }
      ]}>
        {item}
      </Text>
      {settings.userLocation === item && (
        <MaterialIcons name="check" size={20} color="#FFFFFF" />
      )}
    </TouchableOpacity>
  );

  const renderLocationTab = () => (
    <View style={styles.tabContent}>
      {/* Search Input */}
      <View style={[styles.searchContainer, { backgroundColor: themeColors.card }]}>
        <MaterialIcons name="search" size={20} color={themeColors.textSecondary} />
        <TextInput
          style={[styles.searchInput, { color: themeColors.text }]}
          placeholder="Search locations..."
          placeholderTextColor={themeColors.textSecondary}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      {/* Current Location Button */}
      <View style={styles.currentLocationContainer}>
        <TouchableOpacity
          style={[
            styles.currentLocationButton,
            {
              backgroundColor: themeColors.tint,
              opacity: isGettingLocation ? 0.6 : 1
            }
          ]}
          onPress={handleUseCurrentLocation}
          disabled={isGettingLocation}
        >
          {isGettingLocation ? (
            <ActivityIndicator size="small" color="#FFFFFF" />
          ) : (
            <FontAwesome name="location-arrow" size={16} color="#FFFFFF" />
          )}
          <Text style={styles.currentLocationText}>
            {isGettingLocation ? 'Getting location...' : 'Use Current Location'}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Auto-update Location Toggle */}
      <View style={[styles.settingRow, { borderColor: themeColors.border }]}>
        <View style={styles.settingInfo}>
          <Text style={[styles.settingTitle, { color: themeColors.text }]}>Auto-update Location</Text>
          <Text style={[styles.settingDescription, { color: themeColors.textSecondary }]}>
            Automatically update your location when you move
          </Text>
        </View>
        <Switch
          value={settings.autoUpdateLocation}
          onValueChange={(value) => setSettings(prev => ({ ...prev, autoUpdateLocation: value }))}
          trackColor={{ false: themeColors.border, true: themeColors.tint }}
          thumbColor={settings.autoUpdateLocation ? '#FFFFFF' : themeColors.textSecondary}
        />
      </View>

      {/* Clear Location Option */}
      {settings.userLocation && (
        <View style={styles.clearLocationContainer}>
          <TouchableOpacity
            style={[styles.clearLocationButton, { borderColor: themeColors.border }]}
            onPress={() => handleLocationSelect('')}
          >
            <MaterialIcons name="clear" size={16} color={themeColors.textSecondary} />
            <Text style={[styles.clearLocationText, { color: themeColors.textSecondary }]}>
              Clear Location
            </Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Popular Locations */}
      <View style={styles.locationsContainer}>
        <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
          {searchQuery ? 'Search Results' : 'Popular Locations'}
        </Text>
        <FlatList
          data={filteredLocations}
          renderItem={renderLocationItem}
          keyExtractor={(item) => item}
          style={styles.locationsList}
          showsVerticalScrollIndicator={false}
        />
      </View>
    </View>
  );

  const renderRadiusTab = () => (
    <View style={styles.tabContent}>
      {/* Current Location Display */}
      {settings.userLocation && (
        <View style={[styles.currentLocationDisplay, { backgroundColor: themeColors.card }]}>
          <MaterialIcons name="location-on" size={20} color={themeColors.tint} />
          <Text style={[styles.currentLocationDisplayText, { color: themeColors.text }]}>
            {settings.userLocation}
          </Text>
        </View>
      )}

      {/* Search Radius Slider */}
      <View style={styles.radiusContainer}>
        <Text style={[styles.sectionTitle, { color: themeColors.text }]}>Search Radius</Text>
        <View style={styles.radiusValueContainer}>
          <Text style={[styles.radiusValue, { color: themeColors.tint }]}>
            {settings.searchRadius} miles
          </Text>
        </View>
        
        <View style={styles.sliderContainer}>
          <Slider
            style={styles.slider}
            minimumValue={5}
            maximumValue={100}
            value={settings.searchRadius}
            onValueChange={handleRadiusChange}
            minimumTrackTintColor={themeColors.tint}
            maximumTrackTintColor={themeColors.border}
            thumbTintColor={themeColors.tint}
            step={5}
          />
          <View style={styles.sliderLabels}>
            <Text style={[styles.sliderLabel, { color: themeColors.textSecondary }]}>5 mi</Text>
            <Text style={[styles.sliderLabel, { color: themeColors.textSecondary }]}>100 mi</Text>
          </View>
        </View>

        <Text style={[styles.radiusDescription, { color: themeColors.textSecondary }]}>
          Jobs within this radius from your location will be included in your feed
        </Text>
      </View>

      {/* Include Remote Jobs Toggle */}
      <View style={[styles.settingRow, { borderColor: themeColors.border }]}>
        <View style={styles.settingInfo}>
          <Text style={[styles.settingTitle, { color: themeColors.text }]}>Include Remote Jobs</Text>
          <Text style={[styles.settingDescription, { color: themeColors.textSecondary }]}>
            Show remote and work-from-home opportunities
          </Text>
        </View>
        <Switch
          value={settings.includeRemote}
          onValueChange={(value) => setSettings(prev => ({ ...prev, includeRemote: value }))}
          trackColor={{ false: themeColors.border, true: themeColors.tint }}
          thumbColor={settings.includeRemote ? '#FFFFFF' : themeColors.textSecondary}
        />
      </View>
    </View>
  );

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <SafeAreaView style={[styles.container, { backgroundColor: themeColors.background }]}>
        {/* Header */}
        <View style={[styles.header, { borderBottomColor: themeColors.border }]}>
          <TouchableOpacity onPress={onClose}>
            <MaterialIcons name="close" size={24} color={themeColors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: themeColors.text }]}>Location Settings</Text>
          <TouchableOpacity onPress={handleSaveSettings}>
            <Text style={[styles.saveButton, { color: themeColors.tint }]}>Save</Text>
          </TouchableOpacity>
        </View>

        {/* Tab Navigation */}
        <View style={[styles.tabNavigation, { borderBottomColor: themeColors.border }]}>
          <TouchableOpacity
            style={[
              styles.tabButton,
              activeTab === 'location' && { borderBottomColor: themeColors.tint }
            ]}
            onPress={() => setActiveTab('location')}
          >
            <MaterialIcons
              name="location-on"
              size={20}
              color={activeTab === 'location' ? themeColors.tint : themeColors.textSecondary}
            />
            <Text style={[
              styles.tabButtonText,
              { color: activeTab === 'location' ? themeColors.tint : themeColors.textSecondary }
            ]}>
              Location
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.tabButton,
              activeTab === 'radius' && { borderBottomColor: themeColors.tint }
            ]}
            onPress={() => setActiveTab('radius')}
          >
            <MaterialIcons
              name="my-location"
              size={20}
              color={activeTab === 'radius' ? themeColors.tint : themeColors.textSecondary}
            />
            <Text style={[
              styles.tabButtonText,
              { color: activeTab === 'radius' ? themeColors.tint : themeColors.textSecondary }
            ]}>
              Radius
            </Text>
          </TouchableOpacity>
        </View>

        {/* Tab Content */}
        {activeTab === 'location' ? renderLocationTab() : renderRadiusTab()}
      </SafeAreaView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  saveButton: {
    fontSize: 16,
    fontWeight: '600',
  },
  tabNavigation: {
    flexDirection: 'row',
    borderBottomWidth: 1,
  },
  tabButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  tabButtonText: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '500',
  },
  tabContent: {
    flex: 1,
    padding: 20,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    marginBottom: 20,
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
  },
  currentLocationContainer: {
    marginBottom: 20,
  },
  currentLocationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 12,
  },
  currentLocationText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    borderBottomWidth: 1,
    marginBottom: 20,
  },
  settingInfo: {
    flex: 1,
    marginRight: 16,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  clearLocationContainer: {
    marginBottom: 20,
  },
  clearLocationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
  },
  clearLocationText: {
    fontSize: 16,
    marginLeft: 8,
  },
  locationsContainer: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  locationsList: {
    flex: 1,
  },
  locationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    marginBottom: 8,
    borderWidth: 1,
  },
  locationText: {
    flex: 1,
    fontSize: 16,
    marginLeft: 12,
  },
  currentLocationDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    marginBottom: 24,
  },
  currentLocationDisplayText: {
    fontSize: 16,
    marginLeft: 12,
    fontWeight: '500',
  },
  radiusContainer: {
    marginBottom: 32,
  },
  radiusValueContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  radiusValue: {
    fontSize: 24,
    fontWeight: '700',
  },
  sliderContainer: {
    marginBottom: 16,
  },
  slider: {
    width: '100%',
    height: 40,
  },
  sliderLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  sliderLabel: {
    fontSize: 12,
  },
  radiusDescription: {
    fontSize: 14,
    lineHeight: 20,
    textAlign: 'center',
  },
});

export { LocationSettings };