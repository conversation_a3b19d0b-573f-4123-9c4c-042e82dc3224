var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.SliderTrackMark=void 0;var _react=_interopRequireDefault(require("react"));var _reactNative=require("react-native");var _styles=require("../utils/styles");var _jsxRuntime=require("react/jsx-runtime");var _this=this,_jsxFileName="/Users/<USER>/Desktop/Projects/react-native-slider/package/src/components/TrackMark.tsx";var SliderTrackMark=exports.SliderTrackMark=function SliderTrackMark(_ref){var isTrue=_ref.isTrue,index=_ref.index,thumbImage=_ref.thumbImage,StepMarker=_ref.StepMarker,currentValue=_ref.currentValue,min=_ref.min,max=_ref.max;return(0,_jsxRuntime.jsxs)(_reactNative.View,{style:_styles.styles.trackMarkContainer,children:[StepMarker?(0,_jsxRuntime.jsx)(StepMarker,{stepMarked:isTrue,index:index,currentValue:currentValue,min:min,max:max}):null,thumbImage&&isTrue?(0,_jsxRuntime.jsx)(_reactNative.View,{style:_styles.styles.thumbImageContainer,children:(0,_jsxRuntime.jsx)(_reactNative.Image,{source:thumbImage,style:_styles.styles.thumbImage})}):null]});};